import { 
  h, 
  setSignal, 
  setEffect, 
  setMemo,
  batch,
  createStore,
  createResource,
  createReactiveArray,
  createComputed
} from "./olova.js"

// Enhanced App demonstrating improved reactivity
export default function EnhancedApp() {
  // Basic signals with batching
  const [count, setCount] = setSignal(0);
  const [multiplier, setMultiplier] = setSignal(2);
  
  // Computed value with automatic dependency tracking
  const computedValue = createComputed(() => count() * multiplier());
  
  // Store for complex state management
  const userStore = createStore({
    name: 'John',
    age: 25,
    preferences: { theme: 'dark' }
  });
  
  // Reactive array
  const [todos, todoMethods] = createReactiveArray([
    { id: 1, text: 'Learn reactivity', done: false },
    { id: 2, text: 'Build awesome apps', done: false }
  ]);
  
  // Resource for async data
  const [userResource, fetchUser] = createResource(async (userId) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { id: userId, name: `User ${userId}`, email: `user${userId}@example.com` };
  });
  
  // Effect with cleanup
  setEffect(() => {
    console.log('Count changed to:', count());
    
    // Return cleanup function
    return () => {
      console.log('Cleaning up effect for count:', count());
    };
  });
  
  // Batched updates for performance
  const handleBatchedUpdate = () => {
    batch(() => {
      setCount(count() + 1);
      setMultiplier(multiplier() + 1);
      // Both updates happen together, triggering effects only once
    });
  };
  
  const addTodo = () => {
    const newTodo = {
      id: Date.now(),
      text: `Todo ${todos().length + 1}`,
      done: false
    };
    todoMethods.push(newTodo);
  };
  
  const toggleTodo = (id) => {
    todoMethods.map(todo => 
      todo.id === id ? { ...todo, done: !todo.done } : todo
    );
  };
  
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Enhanced Reactivity Demo</h1>
      
      {/* Basic Counter */}
      <section style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Basic Counter</h2>
        <p>Count: {() => count()}</p>
        <p>Multiplier: {() => multiplier()}</p>
        <p>Computed (count × multiplier): {() => computedValue()}</p>
        <button onClick={() => setCount(count() + 1)}>Increment Count</button>
        <button onClick={() => setMultiplier(multiplier() + 1)}>Increment Multiplier</button>
        <button onClick={handleBatchedUpdate}>Batched Update</button>
      </section>
      
      {/* Store Demo */}
      <section style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Store Demo</h2>
        <p>Name: {() => userStore.state.name}</p>
        <p>Age: {() => userStore.state.age}</p>
        <p>Theme: {() => userStore.state.preferences.theme}</p>
        <button onClick={() => userStore.setState(state => ({ 
          ...state, 
          age: state.age + 1 
        }))}>
          Age Up
        </button>
        <button onClick={() => userStore.setState(state => ({ 
          ...state, 
          preferences: { 
            ...state.preferences, 
            theme: state.preferences.theme === 'dark' ? 'light' : 'dark' 
          }
        }))}>
          Toggle Theme
        </button>
      </section>
      
      {/* Reactive Array Demo */}
      <section style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Reactive Array Demo</h2>
        <p>Todo Count: {() => todos().length}</p>
        <button onClick={addTodo}>Add Todo</button>
        <button onClick={() => todoMethods.clear()}>Clear All</button>
        <ul>
          {() => todos().map(todo => (
            <li key={todo.id} style={{ 
              textDecoration: todo.done ? 'line-through' : 'none',
              cursor: 'pointer'
            }} onClick={() => toggleTodo(todo.id)}>
              {todo.text} {todo.done ? '✓' : '○'}
            </li>
          ))}
        </ul>
      </section>
      
      {/* Resource Demo */}
      <section style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h2>Async Resource Demo</h2>
        <button onClick={() => fetchUser(Math.floor(Math.random() * 100))}>
          Fetch Random User
        </button>
        <div>
          {() => {
            const resource = userResource();
            if (resource.loading) return <p>Loading...</p>;
            if (resource.error) return <p>Error: {resource.error.message}</p>;
            if (resource.data) return (
              <div>
                <p>ID: {resource.data.id}</p>
                <p>Name: {resource.data.name}</p>
                <p>Email: {resource.data.email}</p>
              </div>
            );
            return <p>Click "Fetch Random User" to load data</p>;
          }}
        </div>
      </section>
    </div>
  );
}

